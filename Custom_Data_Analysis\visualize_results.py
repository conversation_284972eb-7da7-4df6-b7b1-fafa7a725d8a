#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Visualization and analysis script for custom RF estimation results

This script loads the trained models and generates visualizations similar to 
the original project's output.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import scipy.io as sio
from tensorflow import keras
import sys

# Add the original support files to path
sys.path.insert(0, '../Simulated Neuron/SupportFiles/')
from k_functions import plotGaussMap, plotReconstruction, conv_output_length
from custom_data_sysiden import load_custom_images, load_kilosort_data, compute_neural_responses, custom_kConvNetStyle

def plot_learning_curves(history1, history2, save_path="Results/"):
    """
    Plot learning curves for both training passes
    """
    plt.figure(figsize=(12, 5))
    
    # Pass 1 learning curve
    plt.subplot(1, 2, 1)
    plt.plot(history1.history['loss'], label='Training Loss')
    plt.plot(history1.history['val_loss'], label='Validation Loss')
    plt.title('Pass 1: Filter Estimation')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # Pass 2 learning curve
    plt.subplot(1, 2, 2)
    plt.plot(history2.history['loss'], label='Training Loss')
    plt.plot(history2.history['val_loss'], label='Validation Loss')
    plt.title('Pass 2: Power Law Refinement')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_path, 'LearningCurves.png'), dpi=300, bbox_inches='tight')
    plt.show()

def extract_and_visualize_rf(model, filter_size, save_path="Results/"):
    """
    Extract and visualize the receptive field from the trained model
    """
    # Get the convolutional layer weights
    conv_weights = model.layers[1].get_weights()[0]  # Assuming conv layer is at index 1
    
    # Extract the filter (receptive field)
    rf_filter = conv_weights[:, :, 0, 0]  # First filter, first channel
    
    # Visualize the receptive field
    plt.figure(figsize=(10, 4))
    
    # Raw filter weights
    plt.subplot(1, 3, 1)
    plt.imshow(rf_filter, cmap='RdBu_r', aspect='equal')
    plt.colorbar()
    plt.title('RF Filter Weights')
    plt.axis('off')
    
    # Absolute values
    plt.subplot(1, 3, 2)
    plt.imshow(np.abs(rf_filter), cmap='hot', aspect='equal')
    plt.colorbar()
    plt.title('|RF Filter Weights|')
    plt.axis('off')
    
    # Contour plot
    plt.subplot(1, 3, 3)
    x = np.arange(rf_filter.shape[1])
    y = np.arange(rf_filter.shape[0])
    X, Y = np.meshgrid(x, y)
    plt.contour(X, Y, rf_filter, levels=10)
    plt.title('RF Contours')
    plt.axis('equal')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_path, 'RF_FilterWeights.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    return rf_filter

def plot_prelu_parameters(model, save_path="Results/"):
    """
    Plot PReLU parameters from the model
    """
    # Find PReLU layer
    prelu_layer = None
    for layer in model.layers:
        if 'p_re_lu' in layer.name.lower():
            prelu_layer = layer
            break
    
    if prelu_layer is not None:
        alpha_values = prelu_layer.get_weights()[0]
        
        plt.figure(figsize=(8, 6))
        if alpha_values.ndim > 1:
            plt.imshow(alpha_values.squeeze(), cmap='viridis', aspect='equal')
            plt.colorbar()
            plt.title('PReLU Alpha Parameters')
        else:
            plt.bar(range(len(alpha_values)), alpha_values.flatten())
            plt.title('PReLU Alpha Parameters')
            plt.xlabel('Parameter Index')
            plt.ylabel('Alpha Value')
        
        plt.savefig(os.path.join(save_path, 'PReLU_Alpha.png'), dpi=300, bbox_inches='tight')
        plt.show()
    else:
        print("PReLU layer not found in model")

def plot_gaussian_map(model, conv_output_size, save_path="Results/"):
    """
    Plot the Gaussian map layer parameters
    """
    # Find Gaussian map layer
    gauss_layer = None
    for layer in model.layers:
        if 'gaussian' in layer.name.lower():
            gauss_layer = layer
            break
    
    if gauss_layer is not None:
        try:
            # This depends on the specific implementation of the Gaussian layer
            plotGaussMap(model, conv_output_size, save_path)
        except Exception as e:
            print(f"Could not plot Gaussian map: {e}")
    else:
        print("Gaussian map layer not found in model")

def evaluate_model_performance(model, test_data, test_responses, save_path="Results/"):
    """
    Evaluate model performance and plot actual vs predicted responses
    """
    # Make predictions
    predictions = model.predict(test_data)
    
    # Calculate correlation
    correlation = np.corrcoef(test_responses.flatten(), predictions.flatten())[0, 1]
    
    # Plot actual vs predicted
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(test_responses, predictions, alpha=0.6)
    plt.plot([test_responses.min(), test_responses.max()], 
             [test_responses.min(), test_responses.max()], 'r--', lw=2)
    plt.xlabel('Actual Response')
    plt.ylabel('Predicted Response')
    plt.title(f'Actual vs Predicted\nCorrelation: {correlation:.3f}')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(test_responses[:100], label='Actual', alpha=0.7)
    plt.plot(predictions[:100], label='Predicted', alpha=0.7)
    plt.xlabel('Stimulus Index')
    plt.ylabel('Response')
    plt.title('Response Time Series (First 100 stimuli)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_path, 'ActualPredictedResponse.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Model Performance:")
    print(f"Correlation: {correlation:.3f}")
    print(f"MSE: {np.mean((test_responses - predictions.flatten())**2):.3f}")
    print(f"R²: {1 - np.sum((test_responses - predictions.flatten())**2) / np.sum((test_responses - np.mean(test_responses))**2):.3f}")

def main():
    """
    Main visualization function
    """
    # Configuration - update these paths
    IMAGE_FILE = "example_data/Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat"
    KILOSORT_FILE = "example_data/kilosort_results.mat"
    NEURON_ID = 0
    
    # Check if models exist
    if not os.path.exists("Results/best_model_pass2.keras"):
        print("Error: Trained models not found. Please run custom_data_sysiden.py first.")
        return
    
    print("=== Loading Data and Models ===")
    
    # Load data (same as training script)
    stim = load_custom_images(IMAGE_FILE)
    movieSize = np.shape(stim)
    imSize = (movieSize[0], movieSize[1])
    movieLength = movieSize[2]
    
    spike_times, stimulus_indices = load_kilosort_data(KILOSORT_FILE, NEURON_ID)
    neural_responses = compute_neural_responses(spike_times, stimulus_indices, movieLength)
    
    # Prepare test data
    stim = np.transpose(np.reshape(stim, (imSize[0]*imSize[1], movieLength)))
    predIdx = slice(int(0.9*movieLength), movieLength)
    predSet = stim[predIdx]
    y_pred = neural_responses[predIdx]
    
    # Normalize and add temporal dynamics (same as training)
    from k_functions import buildCalcAndScale
    predSet = buildCalcAndScale(predSet)
    Frames = list(range(4))
    predSet = custom_kConvNetStyle(predSet, Frames)
    
    # Load trained models
    print("Loading trained models...")
    model1 = keras.models.load_model("Results/best_model_pass1.keras", compile=False)
    model2 = keras.models.load_model("Results/best_model_pass2.keras", compile=False)
    
    # Model parameters
    Input_Shape = predSet.shape[1:]
    Filter_Size = 15
    Stride = 1
    convImageSize = conv_output_length(Input_Shape[0], Filter_Size, 'valid', Stride)
    
    print("=== Generating Visualizations ===")
    
    # 1. Extract and visualize receptive field
    print("1. Visualizing receptive field...")
    rf_filter = extract_and_visualize_rf(model2, Filter_Size)
    
    # 2. Plot PReLU parameters
    print("2. Plotting PReLU parameters...")
    plot_prelu_parameters(model2)
    
    # 3. Plot Gaussian map (if available)
    print("3. Plotting Gaussian map...")
    plot_gaussian_map(model2, convImageSize)
    
    # 4. Evaluate model performance
    print("4. Evaluating model performance...")
    evaluate_model_performance(model2, predSet, y_pred)
    
    # 5. Save receptive field data
    print("5. Saving results...")
    results = {
        'rf_filter': rf_filter,
        'correlation': np.corrcoef(y_pred.flatten(), model2.predict(predSet).flatten())[0, 1],
        'neuron_id': NEURON_ID,
        'image_size': imSize,
        'filter_size': Filter_Size
    }
    
    sio.savemat('Results/analysis_results.mat', results)
    
    print("=== Analysis Complete ===")
    print("All results saved in 'Results/' directory")

if __name__ == "__main__":
    main()
