# 快速开始指南

## 0. 安装依赖包

首先检查并安装所需的Python包：

```bash
cd Custom_Data_Analysis
python install_requirements.py
```

这将检查并安装以下必需包：
- numpy, scipy, matplotlib
- tensorflow
- h5py (用于MATLAB v7.3文件)
- pandas, seaborn
- scikit-learn

## 1. 准备你的数据

### 图像数据文件
将你的图像数据文件放在 `example_data/` 文件夹中：
```
example_data/Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat
```

**数据格式要求：**
- 文件包含名为 `imgset` 的变量
- `imgset` 是 24000×1 的cell数组
- 每个cell包含 64×64 的double数组（灰度图像）
- 支持MATLAB v7.3 (HDF5)格式和较旧的.mat格式

### Kilosort结果文件
将你的Kilosort3结果文件也放在 `example_data/` 文件夹中：
```
example_data/your_kilosort_results.mat
```

**数据格式要求：**
- 包含 `spike0_kilosort3` 结构体：
  - `time`: spike时间点
  - `template`: 神经元模板序号
- 包含 `ex.CondTest.CondIndex`: 刺激图片序号

## 2. 修改配置

编辑 `config.py` 文件，更新以下设置：

```python
# 更新文件路径
IMAGE_FILE = "example_data/你的图像文件名.mat"
KILOSORT_FILE = "example_data/你的kilosort结果文件名.mat"

# 选择要分析的神经元
NEURON_ID = 0  # 单个神经元分析

# 或者批量分析多个神经元
ANALYZE_MULTIPLE = True
NEURON_IDS = [0, 1, 2, 3, 4]
```

## 3. 验证数据格式

在运行分析之前，验证你的数据格式：

```bash
python validate_data.py
```

这将：
- 检查文件是否存在和格式正确
- 验证MATLAB文件版本（v7.3或更旧）
- 显示数据统计信息
- 生成样本图像预览

## 4. 运行分析

### 单个神经元分析
```bash
python custom_data_sysiden.py
```

### 批量分析多个神经元
```bash
python batch_analysis.py
```

### 生成可视化结果
```bash
python visualize_results.py
```

## 5. 查看结果

结果将保存在 `Results/` 文件夹中：

### 模型文件
- `best_model_pass1_neuron0.h5` - 第一阶段训练的最佳模型
- `best_model_pass2_neuron0.h5` - 第二阶段训练的最佳模型

### 可视化图像
- `RF_FilterWeights_neuron0.png` - 感受野滤波器权重
- `PReLU_Alpha_neuron0.png` - PReLU参数
- `ActualPredictedResponse_neuron0.png` - 实际vs预测响应
- `LearningCurves.png` - 训练学习曲线

### 数据文件
- `analysis_results_neuron0.mat` - 分析结果数据
- `training_history_neuron0.pkl` - 训练历史数据

## 6. 常见问题解决

### MATLAB v7.3文件错误
如果遇到 "Please use HDF reader for matlab v7.3 files" 错误：
```bash
pip install h5py
```

### 内存不足
在 `config.py` 中调整：
```python
BATCH_SIZE = 256  # 减小批大小
BATCH_SIZE_AUTO_ADJUST = True
```

### 训练不收敛
调整学习参数：
```python
LEARNING_RATE = 0.001  # 降低学习率
MAX_EPOCHS = 500       # 增加训练轮数
EARLY_STOP_PATIENCE = 50  # 增加早停耐心值
```

### 文件路径错误
确保：
1. 数据文件在 `example_data/` 文件夹中
2. 文件名在 `config.py` 中正确设置
3. 文件格式符合要求

## 6. 参数调整建议

### 根据你的数据调整滤波器大小
```python
# 对于64x64图像，可以尝试：
FILTER_SIZE = 15  # 默认值
FILTER_SIZE = 11  # 较小的感受野
FILTER_SIZE = 21  # 较大的感受野
```

### 调整响应窗口
```python
STIMULUS_DURATION = 1.0    # 刺激持续时间（秒）
RESPONSE_WINDOW = 0.5      # 响应窗口（秒）
```

### 数据分割比例
```python
TRAIN_RATIO = 0.8      # 80% 训练
VALIDATION_RATIO = 0.1 # 10% 验证
TEST_RATIO = 0.1       # 10% 测试
```

## 7. 高级功能

### 分析多个神经元
设置 `config.py`：
```python
ANALYZE_MULTIPLE = True
NEURON_IDS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
```

然后运行：
```bash
python batch_analysis.py
```

### 自定义可视化
修改 `visualize_results.py` 中的绘图函数来创建自定义可视化。

### 导出结果到MATLAB
结果自动保存为 `.mat` 格式，可以在MATLAB中加载：
```matlab
load('Results/analysis_results_neuron0.mat')
```

## 8. 验证结果

### 检查模型性能
- 相关系数 > 0.3 通常表示良好的拟合
- 验证损失应该接近训练损失（避免过拟合）
- 学习曲线应该平滑下降

### 检查感受野
- 感受野应该显示清晰的空间结构
- PReLU参数应该在合理范围内（通常0.01-1.0）
- 高斯映射应该显示局部化的响应

## 9. 故障排除清单

- [ ] 数据文件存在且路径正确
- [ ] 数据格式符合要求（imgset变量，正确的维度）
- [ ] 神经元ID存在于数据中
- [ ] 有足够的内存运行分析
- [ ] Python环境包含所有必需的包
- [ ] 配置参数合理设置

## 10. 获取帮助

如果遇到问题：
1. 检查控制台输出的错误信息
2. 验证数据格式和文件路径
3. 尝试使用较小的数据集进行测试
4. 调整配置参数
5. 查看生成的日志文件（如果启用）
