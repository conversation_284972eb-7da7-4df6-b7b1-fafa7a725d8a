# 自定义数据感受野估计脚本

这个文件夹包含了适配你的数据格式的感受野估计脚本。

## 数据格式要求

### 1. 图像数据文件 (.mat)
- 文件应包含名为 `imgset` 的变量
- `imgset` 是一个 24000×1 的cell数组
- 每个cell包含一个 64×64 的double数组（灰度图像）
- 示例文件名: `Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat`

### 2. Kilosort3结果文件 (.mat)
- 包含 `spike0_kilosort3` 结构体，其中：
  - `time`: spike发生的时间点
  - `template`: 对应时间点的神经元模板序号
- 包含 `ex.CondTest.CondIndex`: 刺激图片的序号

## 文件结构

```
Custom_Data_Analysis/
├── README.md                    # 本说明文件
├── custom_data_sysiden.py      # 主训练脚本
├── visualize_results.py        # 结果可视化脚本
├── config.py                   # 配置文件（可选）
├── example_data/               # 示例数据文件夹
│   ├── Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat
│   └── kilosort_results.mat
└── Results/                    # 结果输出文件夹
    ├── best_model_pass1.h5
    ├── best_model_pass2.h5
    ├── LearningCurves.png
    ├── RF_FilterWeights.png
    ├── PReLU_Alpha.png
    ├── ActualPredictedResponse.png
    └── analysis_results.mat
```

## 使用步骤

### 1. 准备数据
将你的数据文件放在 `example_data/` 文件夹中：
- 图像数据文件
- Kilosort3结果文件

### 2. 修改配置
在 `custom_data_sysiden.py` 中修改以下配置：

```python
# 在main()函数中修改这些路径
IMAGE_FILE = "example_data/你的图像文件名.mat"
KILOSORT_FILE = "example_data/你的kilosort结果文件名.mat"
NEURON_ID = 0  # 要分析的神经元ID（模板序号）
```

### 3. 运行训练
```bash
cd Custom_Data_Analysis
python custom_data_sysiden.py
```

这将：
- 加载和预处理你的数据
- 训练两阶段CNN模型
- 保存训练好的模型

### 4. 生成可视化结果
```bash
python visualize_results.py
```

这将生成：
- 感受野滤波器权重可视化
- PReLU参数图
- 高斯映射图（如果可用）
- 实际vs预测响应对比
- 学习曲线

## 主要功能说明

### custom_data_sysiden.py
- `load_custom_images()`: 加载自定义格式的图像数据
- `load_kilosort_data()`: 加载Kilosort3结果
- `compute_neural_responses()`: 计算每个刺激的神经元响应
- `custom_kConvNetStyle()`: 适配不同图像尺寸的数据预处理

### visualize_results.py
- `extract_and_visualize_rf()`: 提取和可视化感受野
- `plot_prelu_parameters()`: 绘制PReLU参数
- `evaluate_model_performance()`: 评估模型性能
- `plot_gaussian_map()`: 绘制高斯映射

## 参数调整

根据你的数据特点，可能需要调整以下参数：

### 模型参数
```python
Filter_Size = 15        # 滤波器大小，根据感受野大小调整
Stride = 1             # 步长
Frames = list(range(4)) # 时间帧数
```

### 训练参数
```python
learning_rate = 0.005   # 学习率
batch_size = 750       # 批大小
epochs = 200           # 训练轮数
patience = 20          # 早停耐心值
```

### 数据分割
```python
# 当前设置：80%训练，10%验证，10%测试
estIdx = slice(0, int(0.8*movieLength))
regIdx = slice(int(0.8*movieLength), int(0.9*movieLength))
predIdx = slice(int(0.9*movieLength), movieLength)
```

## 注意事项

1. **内存使用**: 64×64图像比原始30×30图像大4倍，注意内存使用
2. **计算时间**: 更大的图像和更多的数据会增加训练时间
3. **神经元选择**: 确保选择的神经元有足够的spike数据
4. **数据质量**: 检查图像数据的归一化和预处理是否正确

## 故障排除

### 常见问题

1. **内存不足**
   - 减少batch_size
   - 减少图像数量进行测试

2. **模型不收敛**
   - 调整学习率
   - 检查数据预处理
   - 增加训练轮数

3. **文件路径错误**
   - 确保数据文件在正确位置
   - 检查文件名拼写

4. **数据格式问题**
   - 验证.mat文件中的变量名
   - 检查数据类型和维度

## 输出解释

### 模型性能指标
- **Correlation**: 实际响应与预测响应的相关系数
- **MSE**: 均方误差
- **R²**: 决定系数

### 可视化结果
- **RF_FilterWeights.png**: 感受野滤波器的权重可视化
- **PReLU_Alpha.png**: PReLU层的α参数
- **ActualPredictedResponse.png**: 实际vs预测响应对比
- **LearningCurves.png**: 训练过程中的损失曲线

## 扩展功能

可以根据需要添加：
- 多神经元批量分析
- 不同时间窗口的响应分析
- 交叉验证
- 超参数优化
- 更复杂的时间动态建模
