#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch analysis script for multiple neurons

This script runs RF estimation for multiple neurons and generates
comparative analysis results.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from config import *

def run_single_neuron_analysis(neuron_id):
    """
    Run RF estimation for a single neuron
    
    Parameters:
    neuron_id: ID of the neuron to analyze
    
    Returns:
    success: boolean indicating if analysis was successful
    """
    print(f"\n=== Analyzing Neuron {neuron_id} ===")
    
    try:
        # Temporarily modify the global NEURON_ID
        global NEURON_ID
        original_neuron_id = NEURON_ID
        NEURON_ID = neuron_id
        
        # Import and run the main analysis
        from custom_data_sysiden import main
        main()
        
        # Restore original neuron ID
        NEURON_ID = original_neuron_id
        
        print(f"Successfully completed analysis for neuron {neuron_id}")
        return True
        
    except Exception as e:
        print(f"Error analyzing neuron {neuron_id}: {str(e)}")
        return False

def load_analysis_results(neuron_ids):
    """
    Load analysis results for multiple neurons
    
    Parameters:
    neuron_ids: list of neuron IDs
    
    Returns:
    results_dict: dictionary containing results for each neuron
    """
    results_dict = {}
    
    for neuron_id in neuron_ids:
        try:
            # Load model performance data
            import pickle
            history_file = os.path.join(RESULTS_DIR, f'training_history_neuron{neuron_id}.pkl')
            
            if os.path.exists(history_file):
                with open(history_file, 'rb') as f:
                    history_data = pickle.load(f)
                
                # Extract final performance metrics
                final_train_loss = history_data['history2']['loss'][-1]
                final_val_loss = history_data['history2']['val_loss'][-1]
                
                results_dict[neuron_id] = {
                    'final_train_loss': final_train_loss,
                    'final_val_loss': final_val_loss,
                    'history': history_data
                }
                
                print(f"Loaded results for neuron {neuron_id}")
            else:
                print(f"No results found for neuron {neuron_id}")
                
        except Exception as e:
            print(f"Error loading results for neuron {neuron_id}: {str(e)}")
    
    return results_dict

def create_comparative_plots(results_dict, save_path=None):
    """
    Create comparative plots across neurons
    
    Parameters:
    results_dict: dictionary containing results for each neuron
    save_path: path to save plots (optional)
    """
    if not results_dict:
        print("No results to plot")
        return
    
    neuron_ids = list(results_dict.keys())
    
    # 1. Performance comparison bar plot
    plt.figure(figsize=(12, 8))
    
    # Extract performance metrics
    train_losses = [results_dict[nid]['final_train_loss'] for nid in neuron_ids]
    val_losses = [results_dict[nid]['final_val_loss'] for nid in neuron_ids]
    
    x = np.arange(len(neuron_ids))
    width = 0.35
    
    plt.subplot(2, 2, 1)
    plt.bar(x - width/2, train_losses, width, label='Training Loss', alpha=0.8)
    plt.bar(x + width/2, val_losses, width, label='Validation Loss', alpha=0.8)
    plt.xlabel('Neuron ID')
    plt.ylabel('Final Loss')
    plt.title('Final Training Performance Comparison')
    plt.xticks(x, neuron_ids)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. Learning curves comparison
    plt.subplot(2, 2, 2)
    for neuron_id in neuron_ids:
        history = results_dict[neuron_id]['history']['history2']
        plt.plot(history['val_loss'], label=f'Neuron {neuron_id}', alpha=0.7)
    
    plt.xlabel('Epoch')
    plt.ylabel('Validation Loss')
    plt.title('Learning Curves Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. Overfitting analysis
    plt.subplot(2, 2, 3)
    overfitting_ratios = []
    for neuron_id in neuron_ids:
        train_loss = results_dict[neuron_id]['final_train_loss']
        val_loss = results_dict[neuron_id]['final_val_loss']
        overfitting_ratio = val_loss / train_loss if train_loss > 0 else 1
        overfitting_ratios.append(overfitting_ratio)
    
    plt.bar(range(len(neuron_ids)), overfitting_ratios, alpha=0.8)
    plt.axhline(y=1, color='r', linestyle='--', alpha=0.5, label='No overfitting')
    plt.xlabel('Neuron ID')
    plt.ylabel('Validation/Training Loss Ratio')
    plt.title('Overfitting Analysis')
    plt.xticks(range(len(neuron_ids)), neuron_ids)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. Summary statistics
    plt.subplot(2, 2, 4)
    plt.text(0.1, 0.8, f"Number of neurons analyzed: {len(neuron_ids)}", transform=plt.gca().transAxes)
    plt.text(0.1, 0.7, f"Mean final validation loss: {np.mean(val_losses):.4f}", transform=plt.gca().transAxes)
    plt.text(0.1, 0.6, f"Std final validation loss: {np.std(val_losses):.4f}", transform=plt.gca().transAxes)
    plt.text(0.1, 0.5, f"Best performing neuron: {neuron_ids[np.argmin(val_losses)]}", transform=plt.gca().transAxes)
    plt.text(0.1, 0.4, f"Worst performing neuron: {neuron_ids[np.argmax(val_losses)]}", transform=plt.gca().transAxes)
    plt.text(0.1, 0.3, f"Mean overfitting ratio: {np.mean(overfitting_ratios):.3f}", transform=plt.gca().transAxes)
    plt.axis('off')
    plt.title('Summary Statistics')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(os.path.join(save_path, 'batch_analysis_comparison.png'), 
                   dpi=FIGURE_DPI, bbox_inches='tight')
    
    plt.show()

def create_summary_report(results_dict, save_path=None):
    """
    Create a summary report of the batch analysis
    
    Parameters:
    results_dict: dictionary containing results for each neuron
    save_path: path to save the report (optional)
    """
    if not results_dict:
        print("No results to summarize")
        return
    
    # Create summary DataFrame
    summary_data = []
    for neuron_id, results in results_dict.items():
        summary_data.append({
            'Neuron_ID': neuron_id,
            'Final_Train_Loss': results['final_train_loss'],
            'Final_Val_Loss': results['final_val_loss'],
            'Overfitting_Ratio': results['final_val_loss'] / results['final_train_loss'],
            'Converged_Epoch': len(results['history']['history2']['loss'])
        })
    
    df = pd.DataFrame(summary_data)
    
    # Print summary
    print("\n=== BATCH ANALYSIS SUMMARY ===")
    print(df.to_string(index=False))
    print(f"\nBest performing neuron (lowest val loss): {df.loc[df['Final_Val_Loss'].idxmin(), 'Neuron_ID']}")
    print(f"Most stable neuron (lowest overfitting): {df.loc[df['Overfitting_Ratio'].idxmin(), 'Neuron_ID']}")
    print(f"Fastest converging neuron: {df.loc[df['Converged_Epoch'].idxmin(), 'Neuron_ID']}")
    
    # Save to CSV
    if save_path:
        csv_path = os.path.join(save_path, 'batch_analysis_summary.csv')
        df.to_csv(csv_path, index=False)
        print(f"\nSummary saved to: {csv_path}")
    
    return df

def main():
    """
    Main batch analysis function
    """
    print("=== BATCH RF ESTIMATION ANALYSIS ===")
    
    # Check if we should analyze multiple neurons
    if not ANALYZE_MULTIPLE:
        print("ANALYZE_MULTIPLE is set to False in config.py")
        print("Set ANALYZE_MULTIPLE = True and specify NEURON_IDS to run batch analysis")
        return
    
    print(f"Analyzing neurons: {NEURON_IDS}")
    
    # Create results directory
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # Run analysis for each neuron
    successful_analyses = []
    failed_analyses = []
    
    for neuron_id in NEURON_IDS:
        success = run_single_neuron_analysis(neuron_id)
        if success:
            successful_analyses.append(neuron_id)
        else:
            failed_analyses.append(neuron_id)
    
    print(f"\n=== BATCH ANALYSIS COMPLETE ===")
    print(f"Successful analyses: {successful_analyses}")
    print(f"Failed analyses: {failed_analyses}")
    
    if successful_analyses:
        # Load and compare results
        print("\nLoading results for comparison...")
        results_dict = load_analysis_results(successful_analyses)
        
        if results_dict:
            # Create comparative plots
            print("Creating comparative plots...")
            create_comparative_plots(results_dict, RESULTS_DIR)
            
            # Create summary report
            print("Creating summary report...")
            summary_df = create_summary_report(results_dict, RESULTS_DIR)
            
            print(f"\nBatch analysis complete! Results saved in '{RESULTS_DIR}/'")
        else:
            print("No results could be loaded for comparison")
    else:
        print("No successful analyses to compare")

if __name__ == "__main__":
    main()
